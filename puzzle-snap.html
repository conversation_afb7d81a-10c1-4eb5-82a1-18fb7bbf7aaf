<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Puzzle Snap - لعبة قطع البازل المتحركة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #4a5568;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header p {
            color: #718096;
            font-size: 1.1em;
        }

        .controls-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .control-group {
            background: #f7fafc;
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #e2e8f0;
        }

        .control-group h3 {
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 1.2em;
            border-bottom: 2px solid #4299e1;
            padding-bottom: 5px;
        }

        .file-input-wrapper {
            position: relative;
            display: inline-block;
            width: 100%;
            margin-bottom: 15px;
        }

        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-input-label {
            display: block;
            padding: 12px 20px;
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .file-input-label:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 153, 225, 0.4);
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #4a5568;
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .size-templates {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }

        .size-template {
            padding: 8px 12px;
            background: #edf2f7;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            cursor: pointer;
            text-align: center;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .size-template:hover, .size-template.active {
            background: #4299e1;
            color: white;
            border-color: #3182ce;
        }

        .canvas-container {
            position: relative;
            margin: 30px auto;
            border: 3px solid #4a5568;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            background: #000;
        }

        #gameCanvas {
            display: block;
            max-width: 100%;
            height: auto;
        }

        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #ed8936, #dd6b20);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #f56565, #e53e3e);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .images-list {
            background: #f7fafc;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }

        .images-list h3 {
            margin-bottom: 15px;
            color: #2d3748;
        }

        .image-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
            background: white;
            border-radius: 8px;
            margin-bottom: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .image-preview {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 5px;
        }

        .status-indicator {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-ready {
            background: #c6f6d5;
            color: #22543d;
        }

        .status-processing {
            background: #fed7d7;
            color: #742a2a;
        }

        /* الوضع المظلم */
        .dark-mode {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
        }

        .dark-mode .container {
            background: rgba(45, 55, 72, 0.95);
            color: #e2e8f0;
        }

        .dark-mode .control-group {
            background: #2d3748;
            border-color: #4a5568;
        }

        .dark-mode .control-group h3 {
            color: #e2e8f0;
            border-color: #63b3ed;
        }

        .dark-mode .input-group label {
            color: #e2e8f0;
        }

        .dark-mode .input-group input,
        .dark-mode .input-group select {
            background: #4a5568;
            border-color: #718096;
            color: #e2e8f0;
        }

        .dark-mode .size-template {
            background: #4a5568;
            border-color: #718096;
            color: #e2e8f0;
        }

        .dark-mode .images-list {
            background: #2d3748;
        }

        .dark-mode .image-item {
            background: #4a5568;
            color: #e2e8f0;
        }

        /* تحسينات إضافية */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .success-message {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #48bb78;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e2e8f0;
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4299e1, #63b3ed);
            border-radius: 3px;
            transition: width 0.3s ease;
            width: 0%;
        }

        /* تحسينات الاستجابة */
        @media (max-width: 768px) {
            .controls-panel {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
            }

            .header h1 {
                font-size: 2em;
            }

            .container {
                padding: 15px;
                margin: 10px;
            }

            .canvas-container {
                margin: 20px 0;
            }
        }

        @media (max-width: 480px) {
            .size-templates {
                grid-template-columns: repeat(2, 1fr);
            }

            .header h1 {
                font-size: 1.5em;
            }

            .control-group {
                padding: 15px;
            }
        }

        /* تحسينات إمكانية الوصول */
        .btn:focus,
        .input-group input:focus,
        .input-group select:focus {
            outline: 2px solid #4299e1;
            outline-offset: 2px;
        }

        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div style="position: absolute; top: 20px; left: 20px;">
                <button class="btn btn-secondary" onclick="toggleDarkMode()" title="تبديل الوضع المظلم">
                    🌙 الوضع المظلم
                </button>
            </div>
            <h1>🧩 Puzzle Snap</h1>
            <p>قم بإنشاء ألعاب البازل المتحركة الاحترافية</p>
        </div>

        <div class="controls-panel">
            <div class="control-group">
                <h3>📁 رفع الملفات</h3>
                
                <div class="file-input-wrapper">
                    <input type="file" id="imageInput" class="file-input" multiple accept="image/*">
                    <label for="imageInput" class="file-input-label">
                        📷 اختر الصور (متعددة)
                    </label>
                </div>

                <div class="file-input-wrapper">
                    <input type="file" id="backgroundInput" class="file-input" accept="image/*,video/*">
                    <label for="backgroundInput" class="file-input-label">
                        🎬 اختر الخلفية (صورة أو فيديو)
                    </label>
                </div>

                <div class="input-group">
                    <label>📐 قوالب الأحجام:</label>
                    <div class="size-templates">
                        <div class="size-template" data-size="1:1">مربع</div>
                        <div class="size-template" data-size="9:16">تيك توك</div>
                        <div class="size-template" data-size="16:9">يوتيوب</div>
                        <div class="size-template" data-size="4:5">انستغرام</div>
                        <div class="size-template" data-size="3:4">قصة</div>
                        <div class="size-template active" data-size="custom">مخصص</div>
                    </div>
                </div>

                <div class="input-group">
                    <label>العرض (بكسل):</label>
                    <input type="number" id="canvasWidth" value="800" min="200" max="1920">
                </div>

                <div class="input-group">
                    <label>الارتفاع (بكسل):</label>
                    <input type="number" id="canvasHeight" value="800" min="200" max="1080">
                </div>
            </div>

            <div class="control-group">
                <h3>⚙️ إعدادات القطعة</h3>
                
                <div class="input-group">
                    <label>حجم القطعة (%):</label>
                    <input type="range" id="pieceSize" min="5" max="30" value="15">
                    <span id="pieceSizeValue">15%</span>
                </div>

                <div class="input-group">
                    <label>نوع القطعة:</label>
                    <select id="pieceShape">
                        <option value="circle">دائرة</option>
                        <option value="square">مربع</option>
                        <option value="triangle">مثلث</option>
                        <option value="star">نجمة</option>
                        <option value="heart">قلب</option>
                    </select>
                </div>

                <div class="input-group">
                    <label>لون الثقب:</label>
                    <select id="holeColor">
                        <option value="transparent">شفاف</option>
                        <option value="white">أبيض</option>
                        <option value="black">أسود</option>
                        <option value="custom">لون مخصص</option>
                    </select>
                </div>

                <div class="input-group" id="customColorGroup" style="display: none;">
                    <label>اللون المخصص:</label>
                    <input type="color" id="customColor" value="#ffffff">
                </div>

                <div class="input-group">
                    <label>سرعة الحركة:</label>
                    <input type="range" id="speed" min="1" max="20" value="5">
                    <span id="speedValue">5</span>
                </div>

                <div class="input-group">
                    <label>نوع الحركة:</label>
                    <select id="movementType">
                        <option value="circular">دائرية</option>
                        <option value="linear">خطية</option>
                        <option value="random">عشوائية</option>
                        <option value="zigzag">متعرجة</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="canvas-container">
            <canvas id="gameCanvas" width="800" height="800"></canvas>
        </div>

        <div class="action-buttons">
            <button class="btn btn-primary" id="startBtn">▶️ بدء اللعبة</button>
            <button class="btn btn-secondary" id="stopBtn" disabled>⏹️ إيقاف</button>
            <button class="btn btn-danger" id="resetBtn">🔄 إعادة تعيين</button>
            <button class="btn btn-primary" id="exportBtn">💾 تصدير الكل</button>
        </div>

        <div class="images-list" id="imagesList">
            <h3>📋 قائمة الصور المحملة</h3>
            <div id="imagesContainer">
                <p style="text-align: center; color: #718096;">لم يتم تحميل أي صور بعد</p>
            </div>
        </div>
    </div>

    <script>
        class PuzzleSnapGame {
            constructor() {
                this.canvas = document.getElementById('gameCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.images = [];
                this.currentImageIndex = 0;
                this.background = null;
                this.isPlaying = false;
                this.animationId = null;

                // إعدادات القطعة
                this.piece = {
                    x: 0,
                    y: 0,
                    originalX: 0,
                    originalY: 0,
                    size: 0,
                    angle: 0,
                    speed: 5,
                    shape: 'circle',
                    movementType: 'circular'
                };

                this.initializeEventListeners();
                this.setupCanvas();
            }

            initializeEventListeners() {
                // رفع الصور
                document.getElementById('imageInput').addEventListener('change', (e) => {
                    this.loadImages(e.target.files);
                });

                // رفع الخلفية
                document.getElementById('backgroundInput').addEventListener('change', (e) => {
                    this.loadBackground(e.target.files[0]);
                });

                // قوالب الأحجام
                document.querySelectorAll('.size-template').forEach(template => {
                    template.addEventListener('click', (e) => {
                        this.selectSizeTemplate(e.target);
                    });
                });

                // تحديث الإعدادات
                document.getElementById('pieceSize').addEventListener('input', (e) => {
                    document.getElementById('pieceSizeValue').textContent = e.target.value + '%';
                    this.updatePieceSettings();
                });

                document.getElementById('speed').addEventListener('input', (e) => {
                    document.getElementById('speedValue').textContent = e.target.value;
                    this.piece.speed = parseInt(e.target.value);
                });

                document.getElementById('holeColor').addEventListener('change', (e) => {
                    const customGroup = document.getElementById('customColorGroup');
                    customGroup.style.display = e.target.value === 'custom' ? 'block' : 'none';
                });

                // أزرار التحكم
                document.getElementById('startBtn').addEventListener('click', () => this.startGame());
                document.getElementById('stopBtn').addEventListener('click', () => this.stopGame());
                document.getElementById('resetBtn').addEventListener('click', () => this.resetGame());
                document.getElementById('exportBtn').addEventListener('click', () => this.exportAll());

                // تحديث أبعاد الكانفاس
                document.getElementById('canvasWidth').addEventListener('input', () => this.updateCanvasSize());
                document.getElementById('canvasHeight').addEventListener('input', () => this.updateCanvasSize());

                // النقر على الكانفاس لإيقاف القطعة
                this.canvas.addEventListener('click', (e) => {
                    if (this.isPlaying) {
                        this.checkPiecePosition(e);
                    }
                });
            }

            setupCanvas() {
                this.updateCanvasSize();
                this.drawInitialState();
            }

            updateCanvasSize() {
                const width = parseInt(document.getElementById('canvasWidth').value);
                const height = parseInt(document.getElementById('canvasHeight').value);

                this.canvas.width = width;
                this.canvas.height = height;

                this.drawInitialState();
            }

            selectSizeTemplate(template) {
                document.querySelectorAll('.size-template').forEach(t => t.classList.remove('active'));
                template.classList.add('active');

                const size = template.dataset.size;
                const widthInput = document.getElementById('canvasWidth');
                const heightInput = document.getElementById('canvasHeight');

                if (size !== 'custom') {
                    const [w, h] = size.split(':').map(Number);
                    const baseSize = 800;

                    if (w > h) {
                        widthInput.value = baseSize;
                        heightInput.value = Math.round(baseSize * h / w);
                    } else {
                        heightInput.value = baseSize;
                        widthInput.value = Math.round(baseSize * w / h);
                    }

                    this.updateCanvasSize();
                }
            }

            async loadImages(files) {
                const container = document.getElementById('imagesContainer');
                container.innerHTML = '';
                this.images = [];

                for (let file of files) {
                    const img = new Image();
                    const url = URL.createObjectURL(file);

                    await new Promise((resolve) => {
                        img.onload = () => {
                            this.images.push({
                                image: img,
                                name: file.name,
                                url: url,
                                processed: false
                            });

                            this.addImageToList(img, file.name);
                            resolve();
                        };
                        img.src = url;
                    });
                }

                if (this.images.length > 0) {
                    this.currentImageIndex = 0;
                    this.drawCurrentImage();
                }
            }

            addImageToList(img, name) {
                const container = document.getElementById('imagesContainer');
                const item = document.createElement('div');
                item.className = 'image-item';

                item.innerHTML = `
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <img src="${img.src}" class="image-preview" alt="${name}">
                        <span>${name}</span>
                    </div>
                    <span class="status-indicator status-ready">جاهز</span>
                `;

                container.appendChild(item);
            }

            async loadBackground(file) {
                if (!file) return;

                if (file.type.startsWith('video/')) {
                    const video = document.createElement('video');
                    video.src = URL.createObjectURL(file);
                    video.loop = true;
                    video.muted = true;
                    video.autoplay = true;
                    this.background = video;
                } else {
                    const img = new Image();
                    img.src = URL.createObjectURL(file);
                    await new Promise(resolve => {
                        img.onload = resolve;
                    });
                    this.background = img;
                }

                this.drawCurrentImage();
            }

            drawInitialState() {
                this.ctx.fillStyle = '#f0f0f0';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

                this.ctx.fillStyle = '#666';
                this.ctx.font = '24px Arial';
                this.ctx.textAlign = 'center';
                this.ctx.fillText('قم برفع صورة للبدء', this.canvas.width / 2, this.canvas.height / 2);
            }

            drawCurrentImage() {
                if (this.images.length === 0) {
                    this.drawInitialState();
                    return;
                }

                const currentImg = this.images[this.currentImageIndex];
                this.drawBackground();
                this.drawImageWithHole(currentImg.image);

                if (this.isPlaying) {
                    this.drawMovingPiece();
                }
            }

            drawBackground() {
                if (!this.background) {
                    this.ctx.fillStyle = '#000';
                    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                    return;
                }

                if (this.background.tagName === 'VIDEO') {
                    this.ctx.drawImage(this.background, 0, 0, this.canvas.width, this.canvas.height);
                } else {
                    this.ctx.drawImage(this.background, 0, 0, this.canvas.width, this.canvas.height);
                }
            }

            drawImageWithHole(image) {
                // رسم الصورة الأساسية
                this.ctx.drawImage(image, 0, 0, this.canvas.width, this.canvas.height);

                // إنشاء الثقب
                this.createHole();
            }

            createHole() {
                const size = (parseInt(document.getElementById('pieceSize').value) / 100) * Math.min(this.canvas.width, this.canvas.height);
                const shape = document.getElementById('pieceShape').value;
                const holeColor = document.getElementById('holeColor').value;

                // حفظ موقع القطعة الأصلي
                this.piece.originalX = this.canvas.width / 2;
                this.piece.originalY = this.canvas.height / 2;
                this.piece.size = size;

                // رسم الثقب
                this.ctx.save();
                this.ctx.globalCompositeOperation = 'destination-out';

                this.drawShape(this.piece.originalX, this.piece.originalY, size, shape);

                this.ctx.restore();

                // ملء الثقب باللون المحدد إذا لم يكن شفافاً
                if (holeColor !== 'transparent') {
                    this.ctx.save();
                    this.fillHole(holeColor);
                    this.ctx.restore();
                }
            }

            fillHole(colorType) {
                let fillColor;

                switch(colorType) {
                    case 'white':
                        fillColor = '#ffffff';
                        break;
                    case 'black':
                        fillColor = '#000000';
                        break;
                    case 'custom':
                        fillColor = document.getElementById('customColor').value;
                        break;
                    default:
                        return;
                }

                this.ctx.fillStyle = fillColor;
                this.drawShape(this.piece.originalX, this.piece.originalY, this.piece.size, document.getElementById('pieceShape').value);
            }

            drawShape(x, y, size, shape) {
                this.ctx.beginPath();

                switch(shape) {
                    case 'circle':
                        this.ctx.arc(x, y, size / 2, 0, Math.PI * 2);
                        break;
                    case 'square':
                        this.ctx.rect(x - size / 2, y - size / 2, size, size);
                        break;
                    case 'triangle':
                        this.ctx.moveTo(x, y - size / 2);
                        this.ctx.lineTo(x - size / 2, y + size / 2);
                        this.ctx.lineTo(x + size / 2, y + size / 2);
                        this.ctx.closePath();
                        break;
                    case 'star':
                        this.drawStar(x, y, size / 2, 5);
                        break;
                    case 'heart':
                        this.drawHeart(x, y, size / 2);
                        break;
                }

                this.ctx.fill();
            }

            drawStar(x, y, radius, points) {
                const angle = Math.PI / points;
                this.ctx.beginPath();

                for (let i = 0; i < 2 * points; i++) {
                    const r = i % 2 === 0 ? radius : radius / 2;
                    const currX = x + Math.cos(i * angle) * r;
                    const currY = y + Math.sin(i * angle) * r;

                    if (i === 0) {
                        this.ctx.moveTo(currX, currY);
                    } else {
                        this.ctx.lineTo(currX, currY);
                    }
                }

                this.ctx.closePath();
            }

            drawHeart(x, y, size) {
                this.ctx.beginPath();
                this.ctx.moveTo(x, y + size / 4);

                this.ctx.bezierCurveTo(x, y, x - size / 2, y, x - size / 2, y + size / 4);
                this.ctx.bezierCurveTo(x - size / 2, y + size / 2, x, y + size / 2, x, y + size);
                this.ctx.bezierCurveTo(x, y + size / 2, x + size / 2, y + size / 2, x + size / 2, y + size / 4);
                this.ctx.bezierCurveTo(x + size / 2, y, x, y, x, y + size / 4);

                this.ctx.closePath();
            }

            drawMovingPiece() {
                if (this.images.length === 0) return;

                const currentImg = this.images[this.currentImageIndex];

                // إنشاء قطعة مؤقتة من الصورة الأصلية
                const tempCanvas = document.createElement('canvas');
                const tempCtx = tempCanvas.getContext('2d');
                tempCanvas.width = this.piece.size;
                tempCanvas.height = this.piece.size;

                // رسم جزء من الصورة الأصلية
                const sourceX = this.piece.originalX - this.piece.size / 2;
                const sourceY = this.piece.originalY - this.piece.size / 2;

                tempCtx.drawImage(
                    currentImg.image,
                    sourceX * (currentImg.image.width / this.canvas.width),
                    sourceY * (currentImg.image.height / this.canvas.height),
                    this.piece.size * (currentImg.image.width / this.canvas.width),
                    this.piece.size * (currentImg.image.height / this.canvas.height),
                    0, 0, this.piece.size, this.piece.size
                );

                // قص القطعة بالشكل المحدد
                tempCtx.globalCompositeOperation = 'destination-in';
                this.ctx.save();
                const originalCtx = this.ctx;
                this.ctx = tempCtx;
                this.drawShape(this.piece.size / 2, this.piece.size / 2, this.piece.size, document.getElementById('pieceShape').value);
                this.ctx = originalCtx;
                this.ctx.restore();

                // رسم القطعة المتحركة
                this.ctx.save();
                this.ctx.translate(this.piece.x, this.piece.y);
                this.ctx.rotate(this.piece.angle);
                this.ctx.drawImage(tempCanvas, -this.piece.size / 2, -this.piece.size / 2);
                this.ctx.restore();
            }

            updatePieceSettings() {
                if (this.images.length > 0) {
                    this.drawCurrentImage();
                }
            }

            startGame() {
                if (this.images.length === 0) {
                    alert('يرجى رفع صورة أولاً');
                    return;
                }

                this.isPlaying = true;
                this.initializePiecePosition();

                document.getElementById('startBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;

                this.animate();
            }

            initializePiecePosition() {
                const movementType = document.getElementById('movementType').value;

                switch(movementType) {
                    case 'circular':
                        this.piece.x = this.canvas.width / 2 + 100;
                        this.piece.y = this.canvas.height / 2;
                        break;
                    case 'linear':
                        this.piece.x = 0;
                        this.piece.y = this.canvas.height / 2;
                        break;
                    case 'random':
                        this.piece.x = Math.random() * this.canvas.width;
                        this.piece.y = Math.random() * this.canvas.height;
                        break;
                    case 'zigzag':
                        this.piece.x = 0;
                        this.piece.y = this.canvas.height / 4;
                        break;
                }

                this.piece.angle = 0;
            }

            animate() {
                if (!this.isPlaying) return;

                this.updatePiecePosition();
                this.drawCurrentImage();

                this.animationId = requestAnimationFrame(() => this.animate());
            }

            updatePiecePosition() {
                const movementType = document.getElementById('movementType').value;
                const speed = this.piece.speed / 10;

                switch(movementType) {
                    case 'circular':
                        this.piece.angle += speed;
                        const radius = Math.min(this.canvas.width, this.canvas.height) / 3;
                        this.piece.x = this.canvas.width / 2 + Math.cos(this.piece.angle) * radius;
                        this.piece.y = this.canvas.height / 2 + Math.sin(this.piece.angle) * radius;
                        break;

                    case 'linear':
                        this.piece.x += speed * 10;
                        if (this.piece.x > this.canvas.width + this.piece.size) {
                            this.piece.x = -this.piece.size;
                        }
                        break;

                    case 'random':
                        this.piece.x += (Math.random() - 0.5) * speed * 20;
                        this.piece.y += (Math.random() - 0.5) * speed * 20;

                        // إبقاء القطعة داخل الحدود
                        this.piece.x = Math.max(this.piece.size / 2, Math.min(this.canvas.width - this.piece.size / 2, this.piece.x));
                        this.piece.y = Math.max(this.piece.size / 2, Math.min(this.canvas.height - this.piece.size / 2, this.piece.y));
                        break;

                    case 'zigzag':
                        this.piece.x += speed * 8;
                        this.piece.y = this.canvas.height / 2 + Math.sin(this.piece.x / 50) * 100;

                        if (this.piece.x > this.canvas.width + this.piece.size) {
                            this.piece.x = -this.piece.size;
                        }
                        break;
                }

                // دوران القطعة
                this.piece.angle += speed;
            }

            checkPiecePosition(event) {
                const rect = this.canvas.getBoundingClientRect();
                const clickX = event.clientX - rect.left;
                const clickY = event.clientY - rect.top;

                // تحويل إحداثيات النقر إلى إحداثيات الكانفاس
                const scaleX = this.canvas.width / rect.width;
                const scaleY = this.canvas.height / rect.height;
                const canvasX = clickX * scaleX;
                const canvasY = clickY * scaleY;

                // حساب المسافة بين النقر والموقع الأصلي للقطعة
                const distance = Math.sqrt(
                    Math.pow(canvasX - this.piece.originalX, 2) +
                    Math.pow(canvasY - this.piece.originalY, 2)
                );

                const tolerance = this.piece.size / 2;

                if (distance <= tolerance) {
                    this.winGame();
                } else {
                    this.showMiss(canvasX, canvasY);
                }
            }

            winGame() {
                this.stopGame();

                // رسم القطعة في مكانها الصحيح
                this.piece.x = this.piece.originalX;
                this.piece.y = this.piece.originalY;
                this.piece.angle = 0;
                this.drawCurrentImage();

                // إظهار رسالة الفوز
                this.ctx.save();
                this.ctx.fillStyle = 'rgba(0, 255, 0, 0.8)';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

                this.ctx.fillStyle = 'white';
                this.ctx.font = 'bold 48px Arial';
                this.ctx.textAlign = 'center';
                this.ctx.fillText('🎉 أحسنت!', this.canvas.width / 2, this.canvas.height / 2);
                this.ctx.restore();

                setTimeout(() => {
                    this.nextImage();
                }, 2000);
            }

            showMiss(x, y) {
                // إظهار علامة X في مكان النقر الخاطئ
                this.ctx.save();
                this.ctx.strokeStyle = 'red';
                this.ctx.lineWidth = 5;
                this.ctx.beginPath();
                this.ctx.moveTo(x - 20, y - 20);
                this.ctx.lineTo(x + 20, y + 20);
                this.ctx.moveTo(x + 20, y - 20);
                this.ctx.lineTo(x - 20, y + 20);
                this.ctx.stroke();
                this.ctx.restore();

                setTimeout(() => {
                    this.drawCurrentImage();
                }, 500);
            }

            nextImage() {
                if (this.currentImageIndex < this.images.length - 1) {
                    this.currentImageIndex++;
                    this.drawCurrentImage();
                    this.startGame();
                } else {
                    alert('تهانينا! لقد أكملت جميع الصور!');
                    this.resetGame();
                }
            }

            stopGame() {
                this.isPlaying = false;
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                }

                document.getElementById('startBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
            }

            resetGame() {
                this.stopGame();
                this.currentImageIndex = 0;
                this.drawCurrentImage();
            }

            async exportAll() {
                if (this.images.length === 0) {
                    alert('لا توجد صور للتصدير');
                    return;
                }

                const exportBtn = document.getElementById('exportBtn');
                exportBtn.disabled = true;
                exportBtn.textContent = '⏳ جاري التصدير...';

                try {
                    for (let i = 0; i < this.images.length; i++) {
                        await this.exportSingleImage(i);
                    }

                    alert('تم تصدير جميع الصور بنجاح!');
                } catch (error) {
                    console.error('خطأ في التصدير:', error);
                    alert('حدث خطأ أثناء التصدير');
                } finally {
                    exportBtn.disabled = false;
                    exportBtn.textContent = '💾 تصدير الكل';
                }
            }

            async exportSingleImage(imageIndex) {
                const originalIndex = this.currentImageIndex;
                this.currentImageIndex = imageIndex;

                // إنشاء فيديو HTML
                const htmlContent = this.generateGameHTML(imageIndex);
                this.downloadFile(`puzzle-game-${imageIndex + 1}.html`, htmlContent, 'text/html');

                // إنشاء صورة ثابتة
                this.drawCurrentImage();
                const imageData = this.canvas.toDataURL('image/png');
                this.downloadFile(`puzzle-image-${imageIndex + 1}.png`, imageData, 'image/png');

                this.currentImageIndex = originalIndex;

                // انتظار قصير بين التصديرات
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            generateGameHTML(imageIndex) {
                const image = this.images[imageIndex];
                const settings = this.getCurrentSettings();

                return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Puzzle Game ${imageIndex + 1}</title>
    <style>
        body { margin: 0; padding: 20px; background: #000; display: flex; justify-content: center; align-items: center; min-height: 100vh; }
        canvas { border: 2px solid #fff; border-radius: 10px; max-width: 100%; height: auto; }
        .controls { position: absolute; top: 20px; left: 20px; color: white; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="controls">
        <button onclick="startGame()">▶️ بدء</button>
        <button onclick="stopGame()">⏹️ إيقاف</button>
        <button onclick="resetGame()">🔄 إعادة</button>
    </div>
    <canvas id="gameCanvas" width="${settings.width}" height="${settings.height}"></canvas>
    <script>
        // كود اللعبة المبسط هنا
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        let isPlaying = false;
        let animationId = null;

        const gameImage = new Image();
        gameImage.src = '${image.url}';

        function startGame() { isPlaying = true; animate(); }
        function stopGame() { isPlaying = false; if(animationId) cancelAnimationFrame(animationId); }
        function resetGame() { stopGame(); drawImage(); }
        function animate() { if(!isPlaying) return; drawImage(); animationId = requestAnimationFrame(animate); }
        function drawImage() { ctx.drawImage(gameImage, 0, 0, canvas.width, canvas.height); }

        gameImage.onload = () => drawImage();
    </script>
</body>
</html>`;
            }

            getCurrentSettings() {
                return {
                    width: this.canvas.width,
                    height: this.canvas.height,
                    pieceSize: document.getElementById('pieceSize').value,
                    pieceShape: document.getElementById('pieceShape').value,
                    speed: document.getElementById('speed').value,
                    movementType: document.getElementById('movementType').value,
                    holeColor: document.getElementById('holeColor').value
                };
            }

            downloadFile(filename, content, type) {
                const element = document.createElement('a');

                if (type === 'image/png') {
                    element.href = content;
                } else {
                    const blob = new Blob([content], { type: type });
                    element.href = URL.createObjectURL(blob);
                }

                element.download = filename;
                document.body.appendChild(element);
                element.click();
                document.body.removeChild(element);
            }
        }

        // تهيئة التطبيق
        let game;
        document.addEventListener('DOMContentLoaded', () => {
            game = new PuzzleSnapGame();

            // إضافة بعض التحسينات للواجهة
            setupUIEnhancements();
        });

        function setupUIEnhancements() {
            // تحديث قيم المنزلقات في الوقت الفعلي
            const pieceSize = document.getElementById('pieceSize');
            const speed = document.getElementById('speed');

            pieceSize.addEventListener('input', (e) => {
                document.getElementById('pieceSizeValue').textContent = e.target.value + '%';
            });

            speed.addEventListener('input', (e) => {
                document.getElementById('speedValue').textContent = e.target.value;
            });

            // تحسين تجربة السحب والإفلات
            const canvas = document.getElementById('gameCanvas');

            canvas.addEventListener('dragover', (e) => {
                e.preventDefault();
                canvas.style.opacity = '0.7';
            });

            canvas.addEventListener('dragleave', (e) => {
                canvas.style.opacity = '1';
            });

            canvas.addEventListener('drop', (e) => {
                e.preventDefault();
                canvas.style.opacity = '1';

                const files = Array.from(e.dataTransfer.files).filter(file =>
                    file.type.startsWith('image/')
                );

                if (files.length > 0) {
                    game.loadImages(files);
                }
            });

            // إضافة اختصارات لوحة المفاتيح
            document.addEventListener('keydown', (e) => {
                switch(e.key) {
                    case ' ':
                        e.preventDefault();
                        if (game.isPlaying) {
                            game.stopGame();
                        } else {
                            game.startGame();
                        }
                        break;
                    case 'r':
                    case 'R':
                        game.resetGame();
                        break;
                    case 'ArrowRight':
                        if (game.images.length > 1 && game.currentImageIndex < game.images.length - 1) {
                            game.currentImageIndex++;
                            game.drawCurrentImage();
                        }
                        break;
                    case 'ArrowLeft':
                        if (game.images.length > 1 && game.currentImageIndex > 0) {
                            game.currentImageIndex--;
                            game.drawCurrentImage();
                        }
                        break;
                }
            });

            // إضافة تلميحات للمستخدم
            addTooltips();
        }

        function addTooltips() {
            const tooltips = {
                'imageInput': 'يمكنك اختيار عدة صور في نفس الوقت',
                'backgroundInput': 'يمكن أن تكون صورة أو فيديو للخلفية',
                'pieceSize': 'حجم القطعة المتحركة كنسبة من حجم الشاشة',
                'speed': 'سرعة حركة القطعة (1 = بطيء، 20 = سريع جداً)',
                'startBtn': 'اضغط مسافة للبدء/الإيقاف',
                'gameCanvas': 'اسحب وأفلت الصور هنا أو انقر لإيقاف القطعة'
            };

            Object.entries(tooltips).forEach(([id, text]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.title = text;
                }
            });
        }

        // إضافة دعم للشاشات اللمسية
        let touchStartTime = 0;
        document.getElementById('gameCanvas').addEventListener('touchstart', (e) => {
            touchStartTime = Date.now();
        });

        document.getElementById('gameCanvas').addEventListener('touchend', (e) => {
            const touchDuration = Date.now() - touchStartTime;
            if (touchDuration < 200 && game.isPlaying) { // نقرة سريعة
                const touch = e.changedTouches[0];
                const rect = e.target.getBoundingClientRect();
                const clickEvent = {
                    clientX: touch.clientX,
                    clientY: touch.clientY
                };
                game.checkPiecePosition(clickEvent);
            }
        });

        // منع التكبير على الشاشات اللمسية
        document.addEventListener('touchmove', (e) => {
            if (e.touches.length > 1) {
                e.preventDefault();
            }
        }, { passive: false });

        // إضافة دعم للوضع المظلم
        function toggleDarkMode() {
            document.body.classList.toggle('dark-mode');
        }

        // حفظ الإعدادات في التخزين المحلي
        function saveSettings() {
            const settings = {
                canvasWidth: document.getElementById('canvasWidth').value,
                canvasHeight: document.getElementById('canvasHeight').value,
                pieceSize: document.getElementById('pieceSize').value,
                pieceShape: document.getElementById('pieceShape').value,
                speed: document.getElementById('speed').value,
                movementType: document.getElementById('movementType').value,
                holeColor: document.getElementById('holeColor').value
            };

            localStorage.setItem('puzzleSnapSettings', JSON.stringify(settings));
        }

        function loadSettings() {
            const saved = localStorage.getItem('puzzleSnapSettings');
            if (saved) {
                const settings = JSON.parse(saved);
                Object.entries(settings).forEach(([key, value]) => {
                    const element = document.getElementById(key);
                    if (element) {
                        element.value = value;
                    }
                });
            }
        }

        // تحميل الإعدادات المحفوظة عند بدء التطبيق
        document.addEventListener('DOMContentLoaded', () => {
            loadSettings();
        });

        // حفظ الإعدادات عند تغييرها
        ['canvasWidth', 'canvasHeight', 'pieceSize', 'pieceShape', 'speed', 'movementType', 'holeColor'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', saveSettings);
            }
        });
    </script>
</body>
</html>

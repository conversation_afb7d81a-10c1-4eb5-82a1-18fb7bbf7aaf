/* إضافات وتحسينات إضافية للتطبيق */

/* تأثيرات الحركة المتقدمة */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
    40%, 43% { transform: translateY(-10px); }
    70% { transform: translateY(-5px); }
    90% { transform: translateY(-2px); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* تأثيرات خاصة للأزرار */
.btn-animated {
    position: relative;
    overflow: hidden;
}

.btn-animated::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-animated:hover::before {
    left: 100%;
}

/* تحسينات للقطعة المتحركة */
.piece-glow {
    filter: drop-shadow(0 0 10px rgba(66, 153, 225, 0.8));
}

/* تأثيرات الجسيمات */
.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #4299e1;
    border-radius: 50%;
    pointer-events: none;
    animation: particle-float 2s ease-out forwards;
}

@keyframes particle-float {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-100px) scale(0);
    }
}

/* تحسينات للشاشات عالية الدقة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .canvas-container {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* تأثيرات الانتقال السلسة */
.smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تحسينات للطباعة */
@media print {
    .controls-panel,
    .action-buttons,
    .images-list {
        display: none;
    }
    
    .canvas-container {
        border: 1px solid #000;
        page-break-inside: avoid;
    }
}

/* تأثيرات التحميل */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner-large {
    width: 60px;
    height: 60px;
    border: 6px solid #f3f3f3;
    border-top: 6px solid #4299e1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

/* تحسينات للألعاب */
.game-stats {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px;
    border-radius: 8px;
    font-family: monospace;
    font-size: 14px;
}

.score-popup {
    position: absolute;
    background: #48bb78;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-weight: bold;
    font-size: 18px;
    pointer-events: none;
    animation: score-popup 1s ease-out forwards;
}

@keyframes score-popup {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-50px) scale(1.2);
    }
}

/* تحسينات للتفاعل */
.interactive-hint {
    position: absolute;
    background: rgba(66, 153, 225, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    pointer-events: none;
    animation: hint-pulse 2s infinite;
}

@keyframes hint-pulse {
    0%, 100% { opacity: 0.7; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
}

/* تأثيرات الصوت البصرية */
.sound-wave {
    position: absolute;
    border: 2px solid #4299e1;
    border-radius: 50%;
    animation: sound-wave 1s ease-out forwards;
}

@keyframes sound-wave {
    0% {
        opacity: 1;
        transform: scale(0);
    }
    100% {
        opacity: 0;
        transform: scale(3);
    }
}

/* تحسينات للأداء */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
}

/* تأثيرات خاصة للفوز */
.victory-confetti {
    position: absolute;
    width: 10px;
    height: 10px;
    background: #ffd700;
    animation: confetti-fall 3s ease-out forwards;
}

@keyframes confetti-fall {
    0% {
        opacity: 1;
        transform: translateY(-100px) rotate(0deg);
    }
    100% {
        opacity: 0;
        transform: translateY(100vh) rotate(720deg);
    }
}

/* تحسينات للوصولية */
.focus-visible {
    outline: 3px solid #4299e1;
    outline-offset: 2px;
}

.high-contrast {
    filter: contrast(150%);
}

/* تأثيرات الماء */
.water-ripple {
    position: absolute;
    border: 2px solid rgba(66, 153, 225, 0.6);
    border-radius: 50%;
    animation: ripple 1.5s ease-out forwards;
}

@keyframes ripple {
    0% {
        opacity: 1;
        transform: scale(0);
    }
    100% {
        opacity: 0;
        transform: scale(4);
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 320px) {
    .container {
        padding: 10px;
    }
    
    .control-group {
        padding: 10px;
    }
    
    .btn {
        padding: 8px 15px;
        font-size: 14px;
    }
}

/* تأثيرات الإضاءة */
.neon-glow {
    box-shadow: 
        0 0 5px #4299e1,
        0 0 10px #4299e1,
        0 0 15px #4299e1,
        0 0 20px #4299e1;
}

/* تحسينات للطباعة ثلاثية الأبعاد */
.depth-shadow {
    box-shadow: 
        0 1px 3px rgba(0,0,0,0.12),
        0 1px 2px rgba(0,0,0,0.24),
        0 3px 6px rgba(0,0,0,0.16),
        0 10px 20px rgba(0,0,0,0.19),
        0 15px 25px rgba(0,0,0,0.24);
}

/* تأثيرات الانعكاس */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* تحسينات للحركة السلسة */
.smooth-scroll {
    scroll-behavior: smooth;
}

/* تأثيرات الظلال المتحركة */
.moving-shadow {
    animation: shadow-move 3s ease-in-out infinite;
}

@keyframes shadow-move {
    0%, 100% { box-shadow: 5px 5px 15px rgba(0,0,0,0.3); }
    50% { box-shadow: -5px -5px 15px rgba(0,0,0,0.3); }
}

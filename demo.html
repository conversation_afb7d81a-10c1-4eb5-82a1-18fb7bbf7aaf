<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Puzzle Snap - العرض التوضيحي</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .demo-container {
            max-width: 800px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .demo-header {
            margin-bottom: 30px;
        }

        .demo-header h1 {
            color: #4a5568;
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .demo-header p {
            color: #718096;
            font-size: 1.2em;
            margin-bottom: 20px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .feature-card {
            background: #f7fafc;
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #e2e8f0;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .feature-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 10px;
        }

        .feature-description {
            color: #718096;
            font-size: 0.9em;
            line-height: 1.5;
        }

        .demo-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .demo-btn {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .demo-btn-primary {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
        }

        .demo-btn-secondary {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
        }

        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .demo-preview {
            margin: 30px 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .demo-preview img {
            width: 100%;
            height: auto;
            display: block;
        }

        .instructions {
            background: #e6fffa;
            border: 2px solid #81e6d9;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: right;
        }

        .instructions h3 {
            color: #234e52;
            margin-bottom: 15px;
        }

        .instructions ol {
            color: #2c7a7b;
            line-height: 1.8;
        }

        .instructions li {
            margin-bottom: 8px;
        }

        .tech-stack {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .tech-badge {
            background: #4299e1;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .demo-container {
                margin: 10px;
                padding: 20px;
            }

            .demo-header h1 {
                font-size: 2em;
            }

            .demo-buttons {
                flex-direction: column;
                align-items: center;
            }

            .demo-btn {
                width: 100%;
                max-width: 300px;
            }
        }

        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.1;
        }

        .floating-shape {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="animated-bg" id="animatedBg"></div>
    
    <div class="demo-container">
        <div class="demo-header">
            <h1>🧩 Puzzle Snap</h1>
            <p>تطبيق إنشاء ألعاب البازل المتحركة الاحترافي</p>
            <div class="tech-stack">
                <span class="tech-badge">HTML5 Canvas</span>
                <span class="tech-badge">CSS3 Animations</span>
                <span class="tech-badge">JavaScript ES6+</span>
                <span class="tech-badge">Responsive Design</span>
            </div>
        </div>

        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">📁</div>
                <div class="feature-title">رفع متعدد للملفات</div>
                <div class="feature-description">
                    رفع عدة صور في نفس الوقت مع دعم الخلفيات المتحركة (فيديو)
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🎨</div>
                <div class="feature-title">تخصيص متقدم</div>
                <div class="feature-description">
                    أشكال متنوعة للقطع، ألوان مخصصة، وقوالب أحجام جاهزة
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <div class="feature-title">حركة ديناميكية</div>
                <div class="feature-description">
                    أنماط حركة متنوعة مع تحكم كامل في السرعة والاتجاه
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">💾</div>
                <div class="feature-title">تصدير شامل</div>
                <div class="feature-description">
                    تصدير كملفات HTML تفاعلية أو صور عالية الجودة
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">📱</div>
                <div class="feature-title">تصميم متجاوب</div>
                <div class="feature-description">
                    يعمل بسلاسة على جميع الأجهزة والشاشات
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🌙</div>
                <div class="feature-title">وضع مظلم</div>
                <div class="feature-description">
                    واجهة مريحة للعيون مع حفظ تلقائي للتفضيلات
                </div>
            </div>
        </div>

        <div class="instructions">
            <h3>📋 كيفية الاستخدام:</h3>
            <ol>
                <li>افتح التطبيق الرئيسي</li>
                <li>ارفع صورة أو عدة صور</li>
                <li>اختر خلفية (اختياري)</li>
                <li>خصص إعدادات القطعة والحركة</li>
                <li>ابدأ اللعبة وانقر عندما تكون القطعة في مكانها الصحيح</li>
                <li>صدر النتائج كملفات HTML أو صور</li>
            </ol>
        </div>

        <div class="demo-buttons">
            <a href="puzzle-snap.html" class="demo-btn demo-btn-primary">
                🚀 تشغيل التطبيق
            </a>
            <a href="README.md" class="demo-btn demo-btn-secondary" target="_blank">
                📖 دليل الاستخدام
            </a>
        </div>

        <div class="demo-preview">
            <canvas id="demoCanvas" width="600" height="300" style="background: linear-gradient(45deg, #667eea, #764ba2);"></canvas>
        </div>
    </div>

    <script>
        // إنشاء خلفية متحركة
        function createAnimatedBackground() {
            const bg = document.getElementById('animatedBg');
            const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c'];
            
            for (let i = 0; i < 20; i++) {
                const shape = document.createElement('div');
                shape.className = 'floating-shape';
                shape.style.width = Math.random() * 100 + 50 + 'px';
                shape.style.height = shape.style.width;
                shape.style.left = Math.random() * 100 + '%';
                shape.style.top = Math.random() * 100 + '%';
                shape.style.background = colors[Math.floor(Math.random() * colors.length)];
                shape.style.animationDelay = Math.random() * 6 + 's';
                shape.style.animationDuration = (Math.random() * 4 + 4) + 's';
                bg.appendChild(shape);
            }
        }

        // عرض توضيحي للكانفاس
        function createDemoAnimation() {
            const canvas = document.getElementById('demoCanvas');
            const ctx = canvas.getContext('2d');
            let angle = 0;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // رسم الخلفية
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#667eea');
                gradient.addColorStop(1, '#764ba2');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // رسم النص
                ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🧩 Puzzle Snap Demo', canvas.width / 2, 50);
                
                // رسم قطعة متحركة
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                const radius = 80;
                const pieceX = centerX + Math.cos(angle) * radius;
                const pieceY = centerY + Math.sin(angle) * radius;
                
                // رسم المسار
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
                ctx.lineWidth = 2;
                ctx.setLineDash([5, 5]);
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
                ctx.stroke();
                ctx.setLineDash([]);
                
                // رسم القطعة
                ctx.fillStyle = '#ffd700';
                ctx.strokeStyle = '#ff6b6b';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.arc(pieceX, pieceY, 20, 0, Math.PI * 2);
                ctx.fill();
                ctx.stroke();
                
                // رسم الهدف
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
                ctx.lineWidth = 3;
                ctx.setLineDash([10, 5]);
                ctx.beginPath();
                ctx.arc(centerX + radius, centerY, 25, 0, Math.PI * 2);
                ctx.stroke();
                ctx.setLineDash([]);
                
                angle += 0.02;
                requestAnimationFrame(animate);
            }
            
            animate();
        }

        // تهيئة العرض التوضيحي
        document.addEventListener('DOMContentLoaded', () => {
            createAnimatedBackground();
            createDemoAnimation();
        });
    </script>
</body>
</html>

# 🧩 Puzzle Snap - لعبة قطع البازل المتحركة

تطبيق ويب احترافي لإنشاء ألعاب البازل المتحركة التفاعلية مع إمكانيات متقدمة للتخصيص والتصدير.

## ✨ المميزات الرئيسية

### 📁 إدارة الملفات
- **رفع متعدد للصور**: يمكن رفع عدة صور في نفس الوقت
- **دعم الخلفيات المتنوعة**: صور أو فيديوهات كخلفية
- **معاينة فورية**: عرض مباشر للصور المحملة
- **ضغط تلقائي**: تحسين حجم الملفات للأداء الأمثل

### 🎨 التخصيص المتقدم
- **قوالب أحجام جاهزة**:
  - مربع (1:1)
  - تيك توك (9:16)
  - يو<PERSON>يو<PERSON> (16:9)
  - انستغرام (4:5)
  - قصة (3:4)
  - مخصص

- **أشكال القطع المتنوعة**:
  - دائرة
  - مربع
  - مثلث
  - نجمة
  - قلب

- **خيارات الثقب**:
  - شفاف (يظهر الخلفية)
  - أبيض
  - أسود
  - لون مخصص

### 🎮 أنماط الحركة
- **دائرية**: حركة دائرية منتظمة
- **خطية**: حركة أفقية مستقيمة
- **عشوائية**: حركة غير منتظمة
- **متعرجة**: حركة متموجة

### ⚡ التحكم في السرعة
- نطاق سرعة من 1 (بطيء) إلى 20 (سريع جداً)
- تحديث فوري للسرعة أثناء اللعب
- إعدادات افتراضية محسنة

## 🎯 كيفية اللعب

1. **رفع الصور**: اختر صورة أو عدة صور
2. **اختيار الخلفية**: رفع صورة أو فيديو للخلفية (اختياري)
3. **تخصيص الإعدادات**: حدد حجم وشكل القطعة ونوع الحركة
4. **بدء اللعبة**: اضغط "بدء اللعبة" أو مسافة
5. **الهدف**: انقر عندما تكون القطعة في مكانها الصحيح

## 🎮 التحكم

### لوحة المفاتيح
- **مسافة**: بدء/إيقاف اللعبة
- **R**: إعادة تعيين
- **←/→**: التنقل بين الصور

### الماوس/اللمس
- **نقر على الكانفاس**: إيقاف القطعة
- **سحب وإفلات**: رفع الصور مباشرة على الكانفاس

## 💾 التصدير

### أنواع التصدير المتاحة
- **ملفات HTML**: لعبة تفاعلية كاملة
- **صور PNG**: لقطات ثابتة عالية الجودة
- **تصدير مجمع**: جميع الصور دفعة واحدة

### مميزات التصدير
- تصدير منفصل لكل صورة
- حفظ جميع الإعدادات
- جودة عالية
- أسماء ملفات منظمة

## 🌙 الوضع المظلم

- تبديل سهل بين الوضع العادي والمظلم
- حفظ تلقائي للتفضيلات
- تصميم محسن للعيون

## 📱 الاستجابة

- **دعم كامل للشاشات اللمسية**
- **تصميم متجاوب** لجميع أحجام الشاشات
- **تحسينات خاصة للهواتف المحمولة**

## 🔧 الإعدادات المتقدمة

### حفظ الإعدادات
- حفظ تلقائي في التخزين المحلي
- استرجاع الإعدادات عند إعادة فتح التطبيق
- إعدادات افتراضية محسنة

### تحسينات الأداء
- رسم محسن للكانفاس
- دعم الشاشات عالية الدقة
- تسريع الرسوميات بـ GPU

## 🎨 التأثيرات البصرية

### تأثيرات الفوز
- رسالة تهنئة متحركة
- تأثير الكونفيتي
- انتقال سلس للصورة التالية

### تأثيرات التفاعل
- موجات عند النقر
- جسيمات متحركة
- اهتزاز عند الخطأ

## 🛠️ التقنيات المستخدمة

- **HTML5 Canvas**: للرسم والتفاعل
- **CSS3**: للتصميم والتأثيرات
- **JavaScript ES6+**: للمنطق والتفاعل
- **Web APIs**: للملفات والتخزين

## 📋 متطلبات النظام

- **متصفح حديث** يدعم HTML5 Canvas
- **JavaScript مفعل**
- **ذاكرة كافية** لمعالجة الصور

### المتصفحات المدعومة
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 🚀 التشغيل

1. افتح ملف `puzzle-snap.html` في المتصفح
2. أو ارفع الملفات على خادم ويب
3. لا حاجة لتثبيت إضافي

## 📁 هيكل الملفات

```
puzzle-snap/
├── puzzle-snap.html          # الملف الرئيسي
├── puzzle-snap-styles.css    # تحسينات CSS إضافية
├── puzzle-snap-utils.js      # وظائف مساعدة
└── README.md                 # دليل الاستخدام
```

## 🎯 نصائح للاستخدام الأمثل

### للحصول على أفضل النتائج
- استخدم صور عالية الجودة
- اختر خلفيات متباينة مع الصورة الأساسية
- جرب أحجام قطع مختلفة حسب صعوبة الصورة
- استخدم السرعات المتوسطة للبداية

### للألعاب التحدي
- زد السرعة تدريجياً
- استخدم أشكال قطع معقدة
- اختر خلفيات متحركة
- قلل حجم القطعة

## 🔄 التحديثات المستقبلية

- [ ] دعم الصوت والمؤثرات الصوتية
- [ ] نظام نقاط ومستويات
- [ ] مشاركة النتائج على وسائل التواصل
- [ ] وضع اللعب الجماعي
- [ ] المزيد من أشكال القطع
- [ ] تأثيرات بصرية متقدمة

## 📞 الدعم والمساعدة

إذا واجهت أي مشاكل أو لديك اقتراحات:
- تأكد من تحديث المتصفح
- تحقق من دعم JavaScript
- جرب تصغير حجم الصور إذا كانت كبيرة جداً

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

---

**استمتع بإنشاء ألعاب البازل المتحركة! 🎮✨**
